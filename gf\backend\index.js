import { exec } from "child_process";
import cors from "cors";
import dotenv from "dotenv";
import voice from "elevenlabs-node";
import express from "express";
import { promises as fs } from "fs";
import OpenAI from "openai";
dotenv.config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "-", // Your OpenAI API key here, I used "-" to avoid errors when the key is not set but you should not do that
});

const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;
const voiceID = "kgG7dCoKCfLehAPWkJOE";

// Fallback responses for when OpenAI API is unavailable
const fallbackResponses = {
  quota_exceeded: [
    {
      text: "Oh no! It seems I'm a bit overwhelmed right now. My brain needs a little break!",
      facialExpression: "sad",
      animation: "talking"
    },
    {
      text: "Don't worry though, I'm still here with you. Maybe we can chat again in a little while?",
      facialExpression: "smile",
      animation: "talk222"
    }
  ],
  api_error: [
    {
      text: "Hmm, I'm having some technical difficulties right now. Bear with me!",
      facialExpression: "surprised",
      animation: "talking"
    },
    {
      text: "Sometimes technology can be tricky, but I'm still here for you!",
      facialExpression: "smile",
      animation: "talk222"
    }
  ],
  general_responses: [
    [
      {
        text: "That's really interesting! Tell me more about what you're thinking.",
        facialExpression: "smile",
        animation: "talking"
      }
    ],
    [
      {
        text: "I love hearing from you! You always have such thoughtful things to say.",
        facialExpression: "smile",
        animation: "talk222"
      }
    ],
    [
      {
        text: "You know, that reminds me of something I was thinking about earlier.",
        facialExpression: "default",
        animation: "talking"
      },
      {
        text: "It's amazing how our conversations always lead to new ideas!",
        facialExpression: "smile",
        animation: "talk222"
      }
    ],
    [
      {
        text: "I appreciate you sharing that with me. Your perspective is always so valuable.",
        facialExpression: "smile",
        animation: "talking"
      }
    ]
  ]
};

// Utility function to sleep for retry logic
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Retry function with exponential backoff
const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      // Don't retry on quota errors or authentication errors
      if (error.status === 429 || error.status === 401 || error.status === 403) {
        throw error;
      }

      if (attempt === maxRetries) {
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ API call failed (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`);
      await sleep(delay);
    }
  }
};

const app = express();
app.use(express.json());
app.use(cors());
const port = 3000;

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.get("/voices", async (req, res) => {
  try {
    const voices = await voice.getVoices(elevenLabsApiKey);
    res.send(voices);
  } catch (error) {
    console.error("❌ Error fetching voices:", error.message);
    res.status(500).send({
      error: "Failed to fetch voices",
      message: error.message
    });
  }
});

const execCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) reject(error);
      resolve(stdout);
    });
  });
};

const lipSyncMessage = async (message) => {
  const time = new Date().getTime();
  console.log(`Starting conversion for message ${message}`);
  await execCommand(
    `ffmpeg -y -i audios/message_${message}.mp3 audios/message_${message}.wav`
    // -y to overwrite the file
  );
  console.log(`Conversion done in ${new Date().getTime() - time}ms`);
  await execCommand(
    `./bin/rhubarb -f json -o audios/message_${message}.json audios/message_${message}.wav -r phonetic`
  );
  // -r phonetic is faster but less accurate
  console.log(`Lip sync done in ${new Date().getTime() - time}ms`);
};

app.post("/chat", async (req, res) => {
  const userMessage = req.body.message;
  const timestamp = new Date().toISOString();

  console.log("\n" + "=".repeat(60));
  console.log("📨 NEW CHAT REQUEST");
  console.log("=".repeat(60));
  console.log(`👤 User Message: "${userMessage || 'No message (initial greeting)'}"`);
  console.log(`⏰ Timestamp: ${timestamp}`);

  try {
    if (!userMessage) {
      console.log("🎭 Response Type: Initial Greeting");
      const introMessages = [
        {
          text: "Hey dear... How was your day?",
          audio: await audioFileToBase64("audios/intro_0.wav"),
          lipsync: await readJsonTranscript("audios/intro_0.json"),
          facialExpression: "smile",
          animation: "talking",
        },
        {
          text: "I missed you so much... Please don't go for so long!",
          audio: await audioFileToBase64("audios/intro_1.wav"),
          lipsync: await readJsonTranscript("audios/intro_1.json"),
          facialExpression: "sad",
          animation: "talk222",
        },
      ];

      console.log("✅ Sending initial greeting messages");
      console.log("=".repeat(60) + "\n");
      res.send({ messages: introMessages });
      return;
    }

    if (!elevenLabsApiKey || openai.apiKey === "-") {
      console.log("🚨 Response Type: API Key Error");
      console.log(`❌ Missing API Keys - ElevenLabs: ${!elevenLabsApiKey ? 'Missing' : 'Present'}, OpenAI: ${openai.apiKey === "-" ? 'Missing' : 'Present'}`);

      const apiErrorMessages = [
        {
          text: "Please my dear, don't forget to add your API keys!",
          audio: await audioFileToBase64("audios/api_0.wav"),
          lipsync: await readJsonTranscript("audios/api_0.json"),
          facialExpression: "angry",
          animation: "Angry",
        },
        {
          text: "You don't want to ruin Wawa Sensei with a crazy ChatGPT and ElevenLabs bill, right?",
          audio: await audioFileToBase64("audios/api_1.wav"),
          lipsync: await readJsonTranscript("audios/api_1.json"),
          facialExpression: "smile",
          animation: "Laughing",
        },
      ];

      console.log("✅ Sending API key error messages");
      console.log("=".repeat(60) + "\n");
      res.send({ messages: apiErrorMessages });
      return;
    }

    console.log("🤖 Attempting OpenAI API call...");
    console.log("⚙️  Model: gpt-3.5-turbo-1106");
    console.log("🌡️  Temperature: 0.6");
    console.log("📏 Max Tokens: 1000");

    // Attempt OpenAI API call with retry logic
    const completion = await retryWithBackoff(async () => {
      return await openai.chat.completions.create({
        model: "gpt-3.5-turbo-1106",
        max_tokens: 1000,
        temperature: 0.6,
        response_format: {
          type: "json_object",
        },
        messages: [
          {
            role: "system",
            content: `
            You are a virtual teacher.
            You will always reply with a JSON array of messages. With a maximum of 3 messages.
            Each message has a text, facialExpression, and animation property.
            The different facial expressions are: smile, sad, angry, surprised, funnyFace, laugh and default.
            The different animations are: walk, Thinking, Talking.001, talking, talk222, taking dif, taking, Armature|mixamo.com|layer0
                `,
          },
          {
            role: "user",
            content: userMessage || "Hello",
          },
        ],
      });
    });

    console.log("✅ OpenAI API call successful");
    console.log(`📊 Usage: ${completion.usage?.total_tokens || 'N/A'} tokens`);

    let messages = JSON.parse(completion.choices[0].message.content);
    if (messages.messages) {
      messages = messages.messages; // ChatGPT is not 100% reliable, sometimes it directly returns an array and sometimes a JSON object with a messages property
    }

    console.log("🎭 Response Type: AI Generated");
    console.log("📝 Generated Messages:");
    messages.forEach((msg, index) => {
      console.log(`  ${index + 1}. "${msg.text}" [${msg.facialExpression}] [${msg.animation}]`);
    });

    console.log("⚠️  Audio generation currently disabled");
    console.log("=".repeat(60) + "\n");

    res.send({ messages });
    return;

    // Audio generation code (currently disabled)
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const fileName = `audios/message_${i}.mp3`;
      const textInput = message.text;
      await voice.textToSpeech(elevenLabsApiKey, voiceID, fileName, textInput);
      await lipSyncMessage(i);
      message.audio = await audioFileToBase64(fileName);
      message.lipsync = await readJsonTranscript(`audios/message_${i}.json`);
    }

    res.send({ messages });

  } catch (error) {
    console.error("\n🚨 ERROR OCCURRED:");
    console.error("=".repeat(60));
    console.error(`❌ Error Type: ${error.constructor.name}`);
    console.error(`📝 Error Message: ${error.message}`);
    console.error(`🔢 Status Code: ${error.status || 'N/A'}`);
    console.error(`⏰ Timestamp: ${timestamp}`);

    let fallbackMessages;
    let errorType = "general";

    if (error.status === 429) {
      console.error("💰 QUOTA EXCEEDED - Using quota fallback responses");
      fallbackMessages = fallbackResponses.quota_exceeded;
      errorType = "quota";
    } else if (error.status >= 400 && error.status < 500) {
      console.error("🔑 CLIENT ERROR - Using API error fallback responses");
      fallbackMessages = fallbackResponses.api_error;
      errorType = "client";
    } else if (error.status >= 500) {
      console.error("🔧 SERVER ERROR - Using API error fallback responses");
      fallbackMessages = fallbackResponses.api_error;
      errorType = "server";
    } else {
      console.error("🎲 UNKNOWN ERROR - Using random fallback response");
      const randomIndex = Math.floor(Math.random() * fallbackResponses.general_responses.length);
      fallbackMessages = fallbackResponses.general_responses[randomIndex];
      errorType = "random";
    }

    console.error(`🎭 Fallback Type: ${errorType}`);
    console.error("📝 Fallback Messages:");
    fallbackMessages.forEach((msg, index) => {
      console.error(`  ${index + 1}. "${msg.text}" [${msg.facialExpression}] [${msg.animation}]`);
    });
    console.error("=".repeat(60) + "\n");

    // Send fallback response to frontend
    res.send({
      messages: fallbackMessages,
      error: {
        type: errorType,
        message: error.message,
        status: error.status
      }
    });
  }
});

const readJsonTranscript = async (file) => {
  try {
    const data = await fs.readFile(file, "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.warn(`⚠️  Could not read transcript file: ${file}`);
    return null;
  }
};

const audioFileToBase64 = async (file) => {
  try {
    const data = await fs.readFile(file);
    return data.toString("base64");
  } catch (error) {
    console.warn(`⚠️  Could not read audio file: ${file}`);
    return null;
  }
};

app.listen(port, () => {
  console.log(`Virtual Girlfriend listening on port ${port}`);
});
